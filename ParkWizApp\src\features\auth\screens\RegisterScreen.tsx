import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, Snackbar, ActivityIndicator, Menu, Button as PaperButton } from 'react-native-paper';
import { useAppDispatch, useAppSelector } from '../../../store';
import { register, resetError } from '../../../store';
import api from '../../../services/api.service';

// Components
import TextInput from '../../../components/common/TextInput';
import Button from '../../../components/common/Button';

const RegisterScreen: React.FC = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  // Form state
  const [employeeId, setEmployeeId] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // Role and department state
  const [roles, setRoles] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [selectedRoleId, setSelectedRoleId] = useState('');
  const [selectedRoleName, setSelectedRoleName] = useState('');
  const [selectedDepartmentId, setSelectedDepartmentId] = useState('');
  const [selectedDepartmentName, setSelectedDepartmentName] = useState('');
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(true);
  
  // Menu state
  const [roleMenuVisible, setRoleMenuVisible] = useState(false);
  const [departmentMenuVisible, setDepartmentMenuVisible] = useState(false);
  
  // Role and department error state
  const [roleError, setRoleError] = useState('');
  const [departmentError, setDepartmentError] = useState('');
  
  // Error state
  const [employeeIdError, setEmployeeIdError] = useState('');
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  
  // UI state
  const [snackbarVisible, setSnackbarVisible] = useState(false);

  // Validate form
  const validateForm = (): boolean => {
    let isValid = true;

    // Validate employee ID
    if (!employeeId.trim()) {
      setEmployeeIdError('Employee ID is required');
      isValid = false;
    } else {
      setEmployeeIdError('');
    }

    // Validate first name
    if (!firstName.trim()) {
      setFirstNameError('First name is required');
      isValid = false;
    } else {
      setFirstNameError('');
    }

    // Validate last name
    if (!lastName.trim()) {
      setLastNameError('Last name is required');
      isValid = false;
    } else {
      setLastNameError('');
    }

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Email is invalid');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Validate phone (optional)
    if (phone.trim() && !/^\d{10}$/.test(phone.replace(/[^0-9]/g, ''))) {
      setPhoneError('Phone number is invalid');
      isValid = false;
    } else {
      setPhoneError('');
    }

    // Validate password
    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      isValid = false;
    } else {
      setPasswordError('');
    }

    // Validate confirm password
    if (!confirmPassword) {
      setConfirmPasswordError('Please confirm your password');
      isValid = false;
    } else if (password !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
      isValid = false;
    } else {
      setConfirmPasswordError('');
    }

    // Validate role selection
    if (!selectedRoleId) {
      setRoleError('Please select a role');
      isValid = false;
    } else {
      setRoleError('');
    }

    // Validate department selection
    if (!selectedDepartmentId) {
      setDepartmentError('Please select a department');
      isValid = false;
    } else {
      setDepartmentError('');
    }

    return isValid;
  };

  // Handle registration
  const handleRegister = () => {
    if (validateForm()) {
      console.log('Registration form validated, dispatching register action');
      
      dispatch(register({
        employee_id: employeeId,
        first_name: firstName,
        last_name: lastName,
        email,
        phone,
        password,
        role_id: selectedRoleId,
        department_id: selectedDepartmentId
      }));
    } else {
      console.log('Registration form validation failed');
    }
  };

  // Navigate to login
  const handleNavigateToLogin = () => {
    navigation.navigate('Login');
  };

  // Fetch roles and departments
  useEffect(() => {
    const fetchRolesAndDepartments = async () => {
      setIsLoadingMetadata(true);
      try {
        // Try to fetch roles and departments from the backend
        let rolesData = [];
        let departmentsData = [];
        
        try {
          // Try to fetch roles from the API
          const rolesResponse = await api.get('/api/roles');
          if (rolesResponse.data) {
            // Handle different response structures
            if (Array.isArray(rolesResponse.data)) {
              rolesData = rolesResponse.data;
            } else if (rolesResponse.data.data && Array.isArray(rolesResponse.data.data)) {
              rolesData = rolesResponse.data.data;
            } else if (rolesResponse.data.data && rolesResponse.data.data.roles && Array.isArray(rolesResponse.data.data.roles)) {
              rolesData = rolesResponse.data.data.roles;
            }
            console.log('Fetched roles from API:', rolesData);
          }
          
          // Try to fetch departments from the API
          const departmentsResponse = await api.get('/api/departments');
          if (departmentsResponse.data) {
            // Handle different response structures
            if (Array.isArray(departmentsResponse.data)) {
              departmentsData = departmentsResponse.data;
            } else if (departmentsResponse.data.data && Array.isArray(departmentsResponse.data.data)) {
              departmentsData = departmentsResponse.data.data;
            } else if (departmentsResponse.data.data && departmentsResponse.data.data.departments && Array.isArray(departmentsResponse.data.data.departments)) {
              departmentsData = departmentsResponse.data.data.departments;
            }
            console.log('Fetched departments from API:', departmentsData);
          }
        } catch (apiError) {
          console.log('API endpoints for roles/departments not available, using hardcoded values');
          console.log('API Error details:', apiError);
          
          // Fallback to hardcoded values with proper UUID format
          rolesData = [
            { id: "123e4567-e89b-12d3-a456-************", name: "Engineer" },
            { id: "223e4567-e89b-12d3-a456-************", name: "Manager" },
            { id: "323e4567-e89b-12d3-a456-************", name: "Admin" }
          ];
          
          departmentsData = [
            { id: "423e4567-e89b-12d3-a456-************", name: "Technical Support" },
            { id: "523e4567-e89b-12d3-a456-************", name: "Customer Service" },
            { id: "623e4567-e89b-12d3-a456-************", name: "Development" }
          ];
        }
        
        // If we got empty arrays from the API, use hardcoded values
        if (rolesData.length === 0) {
          rolesData = [
            { id: "123e4567-e89b-12d3-a456-************", name: "Engineer" },
            { id: "223e4567-e89b-12d3-a456-************", name: "Manager" },
            { id: "323e4567-e89b-12d3-a456-************", name: "Admin" }
          ];
        }
        
        if (departmentsData.length === 0) {
          departmentsData = [
            { id: "423e4567-e89b-12d3-a456-************", name: "Technical Support" },
            { id: "523e4567-e89b-12d3-a456-************", name: "Customer Service" },
            { id: "623e4567-e89b-12d3-a456-************", name: "Development" }
          ];
        }
        
        setRoles(rolesData);
        setDepartments(departmentsData);
        
        // Set default selections
        setSelectedRoleId(rolesData[0].id);
        setSelectedRoleName(rolesData[0].name);
        setSelectedDepartmentId(departmentsData[0].id);
        setSelectedDepartmentName(departmentsData[0].name);
      } catch (error) {
        console.error('Error in fetchRolesAndDepartments:', error);
        
        // Fallback to hardcoded values in case of any error
        const rolesData = [
          { id: "123e4567-e89b-12d3-a456-************", name: "Engineer" }
        ];
        
        const departmentsData = [
          { id: "423e4567-e89b-12d3-a456-************", name: "Technical Support" }
        ];
        
        setRoles(rolesData);
        setDepartments(departmentsData);
        setSelectedRoleId(rolesData[0].id);
        setSelectedRoleName(rolesData[0].name);
        setSelectedDepartmentId(departmentsData[0].id);
        setSelectedDepartmentName(departmentsData[0].name);
      } finally {
        setIsLoadingMetadata(false);
      }
    };
    
    fetchRolesAndDepartments();
  }, []);

  // Show error message when registration fails
  useEffect(() => {
    if (error) {
      console.log('Registration error:', error);
      setSnackbarVisible(true);
    }
  }, [error]);

  // Dismiss snackbar
  const dismissSnackbar = () => {
    setSnackbarVisible(false);
    dispatch(resetError());
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.headerContainer}>
          <Text style={styles.headerTitle}>Create Account</Text>
          <Text style={styles.headerSubtitle}>Join ParkWiz Support & Operations Hub</Text>
        </View>

        {isLoadingMetadata ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#0066CC" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : (
          <>
            <View style={styles.formContainer}>
              <TextInput
                label="Employee ID"
                value={employeeId}
                onChangeText={setEmployeeId}
                error={!!employeeIdError}
                errorText={employeeIdError}
                left={<TextInput.Icon icon="card-account-details" />}
                testID="employee-id-input"
              />

              <TextInput
                label="First Name"
                value={firstName}
                onChangeText={setFirstName}
                error={!!firstNameError}
                errorText={firstNameError}
                left={<TextInput.Icon icon="account" />}
                testID="first-name-input"
              />

              <TextInput
                label="Last Name"
                value={lastName}
                onChangeText={setLastName}
                error={!!lastNameError}
                errorText={lastNameError}
                left={<TextInput.Icon icon="account" />}
                testID="last-name-input"
              />

              <TextInput
                label="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                error={!!emailError}
                errorText={emailError}
                left={<TextInput.Icon icon="email" />}
                testID="email-input"
              />

              <TextInput
                label="Phone (optional)"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                error={!!phoneError}
                errorText={phoneError}
                left={<TextInput.Icon icon="phone" />}
                testID="phone-input"
              />

              <TextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                error={!!passwordError}
                errorText={passwordError}
                left={<TextInput.Icon icon="lock" />}
                testID="password-input"
              />

              <TextInput
                label="Confirm Password"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
                error={!!confirmPasswordError}
                errorText={confirmPasswordError}
                left={<TextInput.Icon icon="lock-check" />}
                testID="confirm-password-input"
              />

              {/* Role Selection */}
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>Role</Text>
                <View style={[styles.dropdown, roleError ? styles.dropdownError : null]}>
                  <PaperButton 
                    onPress={() => setRoleMenuVisible(true)} 
                    style={styles.dropdownButton}
                    icon="chevron-down"
                    contentStyle={styles.dropdownButtonContent}
                  >
                    {selectedRoleName || 'Select Role'}
                  </PaperButton>
                  <Menu
                    visible={roleMenuVisible}
                    onDismiss={() => setRoleMenuVisible(false)}
                    anchor={{ x: 0, y: 0 }}
                    style={styles.menu}
                  >
                    {roles.map((role) => (
                      <Menu.Item
                        key={role.id}
                        onPress={() => {
                          setSelectedRoleId(role.id);
                          setSelectedRoleName(role.name);
                          setRoleMenuVisible(false);
                          setRoleError('');
                        }}
                        title={role.name}
                      />
                    ))}
                  </Menu>
                </View>
                {roleError ? <Text style={styles.errorText}>{roleError}</Text> : null}
              </View>

              {/* Department Selection */}
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>Department</Text>
                <View style={[styles.dropdown, departmentError ? styles.dropdownError : null]}>
                  <PaperButton 
                    onPress={() => setDepartmentMenuVisible(true)} 
                    style={styles.dropdownButton}
                    icon="chevron-down"
                    contentStyle={styles.dropdownButtonContent}
                  >
                    {selectedDepartmentName || 'Select Department'}
                  </PaperButton>
                  <Menu
                    visible={departmentMenuVisible}
                    onDismiss={() => setDepartmentMenuVisible(false)}
                    anchor={{ x: 0, y: 0 }}
                    style={styles.menu}
                  >
                    {departments.map((department) => (
                      <Menu.Item
                        key={department.id}
                        onPress={() => {
                          setSelectedDepartmentId(department.id);
                          setSelectedDepartmentName(department.name);
                          setDepartmentMenuVisible(false);
                          setDepartmentError('');
                        }}
                        title={department.name}
                      />
                    ))}
                  </Menu>
                </View>
                {departmentError ? <Text style={styles.errorText}>{departmentError}</Text> : null}
              </View>

              <Button
                mode="contained"
                onPress={handleRegister}
                style={styles.registerButton}
                disabled={isLoading}
                loading={isLoading}
                testID="register-button"
              >
                Register
              </Button>
            </View>

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>Already have an account?</Text>
              <Button
                mode="outlined"
                onPress={handleNavigateToLogin}
                style={styles.loginButton}
                testID="login-button"
              >
                Login
              </Button>
            </View>

            <View style={styles.helpContainer}>
              <Text style={styles.helpText}>
                Contact admin for registration assistance
              </Text>
            </View>
          </>
        )}
      </ScrollView>

      <Snackbar
        visible={snackbarVisible || !!error}
        onDismiss={dismissSnackbar}
        action={{
          label: 'Dismiss',
          onPress: dismissSnackbar,
        }}
        duration={3000}
      >
        {error || 'An error occurred'}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0066CC',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    minHeight: 300,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
  formContainer: {
    marginBottom: 24,
  },
  registerButton: {
    marginTop: 16,
    paddingVertical: 8,
  },
  loginContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  loginText: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 8,
  },
  loginButton: {
    width: '50%',
  },
  helpContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  helpText: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
  // Dropdown styles
  dropdownContainer: {
    marginBottom: 16,
  },
  dropdownLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#CCCCCC',
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
  },
  dropdownError: {
    borderColor: '#FF0000',
  },
  dropdownButton: {
    width: '100%',
    justifyContent: 'flex-start',
  },
  dropdownButtonContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  menu: {
    width: '80%',
  },
  errorText: {
    color: '#FF0000',
    fontSize: 12,
    marginTop: 4,
  },
});

export default RegisterScreen;