import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Platform } from 'react-native';
import { Text, Avatar, Card, Divider, Switch, List, Button, Dialog, Portal, Paragraph } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAppDispatch, useAppSelector } from '../../../store';
import { logout } from '../../../store';

const ProfileHomeScreen: React.FC = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  
  // Settings state
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isPushNotificationsEnabled, setIsPushNotificationsEnabled] = useState(true);
  const [isEmailNotificationsEnabled, setIsEmailNotificationsEnabled] = useState(true);
  const [isBiometricEnabled, setIsBiometricEnabled] = useState(false);
  
  // Dialog state
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  const handleLogout = () => {
    setLogoutDialogVisible(false);
    dispatch(logout());
  };

  // Mock user data
  const userData = {
    id: user?.id || '1',
    employee_id: user?.employee_id || 'EMP001',
    first_name: user?.first_name || 'John',
    last_name: user?.last_name || 'Doe',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    role: 'Engineer',
    department: 'Technical Support',
    manager: 'Sarah Johnson',
    joined_date: '2022-03-15',
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {/* Profile Header */}
      <Card style={styles.profileCard}>
        <Card.Content>
          <View style={styles.profileHeader}>
            <Avatar.Text 
              size={80} 
              label={`${userData.first_name[0]}${userData.last_name[0]}`} 
              backgroundColor="#0066CC" 
            />
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>
                {userData.first_name} {userData.last_name}
              </Text>
              <Text style={styles.profileRole}>
                {userData.role} • {userData.department}
              </Text>
              <Text style={styles.profileEmail}>{userData.email}</Text>
            </View>
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={styles.profileDetails}>
            <View style={styles.detailItem}>
              <Icon name="badge-account" size={20} color="#757575" />
              <Text style={styles.detailLabel}>Employee ID:</Text>
              <Text style={styles.detailValue}>{userData.employee_id}</Text>
            </View>
            <View style={styles.detailItem}>
              <Icon name="phone" size={20} color="#757575" />
              <Text style={styles.detailLabel}>Phone:</Text>
              <Text style={styles.detailValue}>{userData.phone}</Text>
            </View>
            <View style={styles.detailItem}>
              <Icon name="account-supervisor" size={20} color="#757575" />
              <Text style={styles.detailLabel}>Manager:</Text>
              <Text style={styles.detailValue}>{userData.manager}</Text>
            </View>
            <View style={styles.detailItem}>
              <Icon name="calendar" size={20} color="#757575" />
              <Text style={styles.detailLabel}>Joined:</Text>
              <Text style={styles.detailValue}>{userData.joined_date}</Text>
            </View>
          </View>
          
          <View style={styles.profileActions}>
            <Button
              mode="outlined"
              icon="account-edit"
              onPress={() => navigation.navigate('EditProfile')}
              style={styles.profileButton}
            >
              Edit Profile
            </Button>
            <Button
              mode="outlined"
              icon="lock-reset"
              onPress={() => navigation.navigate('ChangePassword')}
              style={styles.profileButton}
            >
              Change Password
            </Button>
          </View>
        </Card.Content>
      </Card>

      {/* Settings */}
      <Card style={styles.settingsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Settings</Text>
          
          <List.Item
            title="Dark Mode"
            description="Enable dark theme"
            left={(props) => <List.Icon {...props} icon="theme-light-dark" />}
            right={() => (
              <Switch
                value={isDarkMode}
                onValueChange={setIsDarkMode}
                color="#0066CC"
              />
            )}
          />
          
          <Divider />
          
          <List.Item
            title="Push Notifications"
            description="Receive push notifications"
            left={(props) => <List.Icon {...props} icon="bell" />}
            right={() => (
              <Switch
                value={isPushNotificationsEnabled}
                onValueChange={setIsPushNotificationsEnabled}
                color="#0066CC"
              />
            )}
          />
          
          <Divider />
          
          <List.Item
            title="Email Notifications"
            description="Receive email notifications"
            left={(props) => <List.Icon {...props} icon="email" />}
            right={() => (
              <Switch
                value={isEmailNotificationsEnabled}
                onValueChange={setIsEmailNotificationsEnabled}
                color="#0066CC"
              />
            )}
          />
          
          <Divider />
          
          <List.Item
            title="Biometric Authentication"
            description="Use fingerprint or face ID"
            left={(props) => <List.Icon {...props} icon="fingerprint" />}
            right={() => (
              <Switch
                value={isBiometricEnabled}
                onValueChange={setIsBiometricEnabled}
                color="#0066CC"
              />
            )}
          />
        </Card.Content>
      </Card>

      {/* Help & Support */}
      <Card style={styles.supportCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Help & Support</Text>
          
          <List.Item
            title="Knowledge Base"
            description="Browse articles and guides"
            left={(props) => <List.Icon {...props} icon="book-open-variant" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => console.log('Open knowledge base')}
          />
          
          <Divider />
          
          <List.Item
            title="Contact Support"
            description="Get help from our team"
            left={(props) => <List.Icon {...props} icon="headset" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => console.log('Contact support')}
          />
          
          <Divider />
          
          <List.Item
            title="Report a Bug"
            description="Help us improve the app"
            left={(props) => <List.Icon {...props} icon="bug" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => console.log('Report bug')}
          />
        </Card.Content>
      </Card>

      {/* About */}
      <Card style={styles.aboutCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>About</Text>
          
          <List.Item
            title="App Version"
            description="1.0.0 (Build 1)"
            left={(props) => <List.Icon {...props} icon="information" />}
          />
          
          <Divider />
          
          <List.Item
            title="Terms of Service"
            left={(props) => <List.Icon {...props} icon="file-document" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => console.log('View terms')}
          />
          
          <Divider />
          
          <List.Item
            title="Privacy Policy"
            left={(props) => <List.Icon {...props} icon="shield-account" />}
            right={(props) => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => console.log('View privacy policy')}
          />
        </Card.Content>
      </Card>

      {/* Logout Button */}
      <Button
        mode="contained"
        icon="logout"
        onPress={() => setLogoutDialogVisible(true)}
        style={styles.logoutButton}
        buttonColor="#F44336"
      >
        Logout
      </Button>
      
      <Text style={styles.copyrightText}>
        © 2023 ParkWiz. All rights reserved.
      </Text>
      
      {/* Logout Confirmation Dialog */}
      <Portal>
        <Dialog visible={logoutDialogVisible} onDismiss={() => setLogoutDialogVisible(false)}>
          <Dialog.Title>Confirm Logout</Dialog.Title>
          <Dialog.Content>
            <Paragraph>Are you sure you want to logout?</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLogoutDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleLogout}>Logout</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  profileCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  profileRole: {
    fontSize: 14,
    color: '#0066CC',
    marginVertical: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#757575',
  },
  divider: {
    marginVertical: 12,
  },
  profileDetails: {
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#757575',
    marginLeft: 8,
    width: 100,
  },
  detailValue: {
    fontSize: 14,
    flex: 1,
  },
  profileActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  profileButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  settingsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  supportCard: {
    marginBottom: 16,
  },
  aboutCard: {
    marginBottom: 24,
  },
  logoutButton: {
    marginBottom: 16,
  },
  copyrightText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#757575',
    marginBottom: 16,
  },
});

export default ProfileHomeScreen;
