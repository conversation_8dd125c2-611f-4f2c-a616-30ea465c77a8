const { Role, Department } = require('../models');
const { AppError } = require('../middleware/error.middleware');

// Get all roles
exports.getAllRoles = async (req, res, next) => {
  try {
    const roles = await Role.findAll({
      attributes: ['id', 'name', 'description']
    });

    res.status(200).json({
      status: 'success',
      data: roles
    });
  } catch (error) {
    next(error);
  }
};

// Get all departments
exports.getAllDepartments = async (req, res, next) => {
  try {
    const departments = await Department.findAll({
      attributes: ['id', 'name', 'description']
    });

    res.status(200).json({
      status: 'success',
      data: departments
    });
  } catch (error) {
    next(error);
  }
};