import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, Button, Avatar, Snackbar } from 'react-native-paper';
import { useAppSelector, useAppDispatch } from '../../../store';
import TextInput from '../../../components/common/TextInput';
import api from '../../../services/api.service';

const EditProfileScreen: React.FC = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  // Form state
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  
  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  
  // Error state
  const [firstNameError, setFirstNameError] = useState('');
  const [lastNameError, setLastNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [phoneError, setPhoneError] = useState('');

  // Load user data
  useEffect(() => {
    if (user) {
      setFirstName(user.first_name || '');
      setLastName(user.last_name || '');
      setEmail(user.email || '');
      setPhone(user.phone || '');
    }
  }, [user]);

  // Validate form
  const validateForm = (): boolean => {
    let isValid = true;

    // Validate first name
    if (!firstName.trim()) {
      setFirstNameError('First name is required');
      isValid = false;
    } else {
      setFirstNameError('');
    }

    // Validate last name
    if (!lastName.trim()) {
      setLastNameError('Last name is required');
      isValid = false;
    } else {
      setLastNameError('');
    }

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Email is invalid');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Validate phone (optional)
    if (phone.trim() && !/^\d{10}$/.test(phone.replace(/[^0-9]/g, ''))) {
      setPhoneError('Phone number is invalid');
      isValid = false;
    } else {
      setPhoneError('');
    }

    return isValid;
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Make API request to update profile
      const response = await api.patch(`/api/users/${user?.id}`, {
        first_name: firstName,
        last_name: lastName,
        email,
        phone
      });

      // Show success message
      setSnackbarMessage('Profile updated successfully');
      setSnackbarVisible(true);

      // Navigate back after a short delay
      setTimeout(() => {
        navigation.goBack();
      }, 1500);
    } catch (error) {
      console.error('Error updating profile:', error);
      setSnackbarMessage('Failed to update profile. Please try again.');
      setSnackbarVisible(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.avatarContainer}>
          <Avatar.Text 
            size={80} 
            label={`${firstName[0] || ''}${lastName[0] || ''}`} 
            backgroundColor="#0066CC" 
          />
          <Text style={styles.avatarText}>Profile Photo</Text>
        </View>

        <View style={styles.formContainer}>
          <TextInput
            label="First Name"
            value={firstName}
            onChangeText={setFirstName}
            error={!!firstNameError}
            errorText={firstNameError}
            left={<TextInput.Icon icon="account" />}
            testID="first-name-input"
          />

          <TextInput
            label="Last Name"
            value={lastName}
            onChangeText={setLastName}
            error={!!lastNameError}
            errorText={lastNameError}
            left={<TextInput.Icon icon="account" />}
            testID="last-name-input"
          />

          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            error={!!emailError}
            errorText={emailError}
            left={<TextInput.Icon icon="email" />}
            testID="email-input"
          />

          <TextInput
            label="Phone (optional)"
            value={phone}
            onChangeText={setPhone}
            keyboardType="phone-pad"
            error={!!phoneError}
            errorText={phoneError}
            left={<TextInput.Icon icon="phone" />}
            testID="phone-input"
          />

          <View style={styles.nonEditableContainer}>
            <Text style={styles.nonEditableLabel}>Employee ID</Text>
            <Text style={styles.nonEditableValue}>{user?.employee_id || 'N/A'}</Text>
          </View>

          <View style={styles.nonEditableContainer}>
            <Text style={styles.nonEditableLabel}>Role</Text>
            <Text style={styles.nonEditableValue}>{user?.role?.name || 'N/A'}</Text>
          </View>

          <View style={styles.nonEditableContainer}>
            <Text style={styles.nonEditableLabel}>Department</Text>
            <Text style={styles.nonEditableValue}>{user?.department?.name || 'N/A'}</Text>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={styles.cancelButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSaveProfile}
              style={styles.saveButton}
              loading={isLoading}
              disabled={isLoading}
            >
              Save Changes
            </Button>
          </View>
        </View>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F7FA',
  },
  scrollContent: {
    padding: 16,
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  avatarText: {
    marginTop: 8,
    fontSize: 14,
    color: '#757575',
  },
  formContainer: {
    marginBottom: 24,
  },
  nonEditableContainer: {
    marginVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingBottom: 8,
  },
  nonEditableLabel: {
    fontSize: 12,
    color: '#757575',
    marginBottom: 4,
  },
  nonEditableValue: {
    fontSize: 16,
    color: '#333333',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  saveButton: {
    flex: 1,
    marginLeft: 8,
  },
});

export default EditProfileScreen;