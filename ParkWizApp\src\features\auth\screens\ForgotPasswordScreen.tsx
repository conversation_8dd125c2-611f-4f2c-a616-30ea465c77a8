import React, { useState } from 'react';
import { View, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { Text, Snackbar } from 'react-native-paper';

// Components
import TextInput from '../../../components/common/TextInput';
import Button from '../../../components/common/Button';

const ForgotPasswordScreen: React.FC = ({ navigation }: any) => {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [resetSent, setResetSent] = useState(false);

  const validateForm = (): boolean => {
    let isValid = true;

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Email is invalid');
      isValid = false;
    } else {
      setEmailError('');
    }

    return isValid;
  };

  const handleResetPassword = async () => {
    if (validateForm()) {
      setIsLoading(true);
      
      // In a real app, you would make an API call to request a password reset
      // For now, we'll simulate a successful request
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Show success message
        setSnackbarMessage('Password reset instructions sent to your email');
        setSnackbarVisible(true);
        setResetSent(true);
      } catch (error) {
        // Show error message
        setSnackbarMessage('Failed to send reset instructions. Please try again.');
        setSnackbarVisible(true);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.headerContainer}>
          <Text style={styles.headerTitle}>Reset Password</Text>
          <Text style={styles.headerSubtitle}>
            Enter your email address and we'll send you instructions to reset your password
          </Text>
        </View>

        {!resetSent ? (
          <View style={styles.formContainer}>
            <TextInput
              label="Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!emailError}
              errorText={emailError}
              left={<TextInput.Icon icon="email" />}
              testID="email-input"
            />

            <Button
              mode="contained"
              onPress={handleResetPassword}
              style={styles.resetButton}
              disabled={isLoading}
              loading={isLoading}
              testID="reset-button"
            >
              Send Reset Instructions
            </Button>
          </View>
        ) : (
          <View style={styles.successContainer}>
            <Text style={styles.successText}>
              We've sent password reset instructions to your email. Please check your inbox and follow the instructions.
            </Text>
          </View>
        )}

        <Button
          mode="outlined"
          onPress={handleBackToLogin}
          style={styles.backButton}
          testID="back-button"
        >
          Back to Login
        </Button>

        <View style={styles.helpContainer}>
          <Text style={styles.helpText}>
            Contact admin if you don't receive the email
          </Text>
        </View>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        action={{
          label: 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
    justifyContent: 'center',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0066CC',
    marginBottom: 16,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#757575',
    textAlign: 'center',
  },
  formContainer: {
    marginBottom: 32,
  },
  resetButton: {
    marginTop: 16,
    paddingVertical: 8,
  },
  successContainer: {
    marginBottom: 32,
    padding: 16,
    backgroundColor: '#e6f7ff',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#0066CC',
  },
  successText: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
  },
  backButton: {
    marginBottom: 24,
  },
  helpContainer: {
    alignItems: 'center',
  },
  helpText: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
});

export default ForgotPasswordScreen;