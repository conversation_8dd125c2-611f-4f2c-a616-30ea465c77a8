import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Image, KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity } from 'react-native';
import { Text, Snackbar } from 'react-native-paper';
import { useAppDispatch, useAppSelector } from '../../../store';
import { login, resetError } from '../../../store';

// Components
import TextInput from '../../../components/common/TextInput';
import Button from '../../../components/common/Button';

// Theme
import theme from '../../../theme/theme';

const LoginScreen: React.FC = ({ navigation }: any) => {
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [snackbarVisible, setSnackbarVisible] = useState(false);

  const validateForm = (): boolean => {
    let isValid = true;

    // Validate email
    if (!email.trim()) {
      setEmailError('Email is required');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setEmailError('Email is invalid');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Validate password
    if (!password) {
      setPasswordError('Password is required');
      isValid = false;
    } else if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
      isValid = false;
    } else {
      setPasswordError('');
    }

    return isValid;
  };

  const handleLogin = () => {
    if (validateForm()) {
      console.log('Login form validated, dispatching login action');
      dispatch(login({ email, password }));
    } else {
      console.log('Login form validation failed');
    }
  };

  // Show error message when authentication fails
  useEffect(() => {
    if (error) {
      console.log('Authentication error:', error);
      setSnackbarVisible(true);
    }
  }, [error]);
  
  // Log loading state changes
  useEffect(() => {
    console.log('Auth loading state:', isLoading);
  }, [isLoading]);

  const handleForgotPassword = () => {
    // Navigate to forgot password screen
    navigation.navigate('ForgotPassword');
  };

  const handleRegister = () => {
    // Navigate to register screen
    navigation.navigate('Register');
  };

  const dismissSnackbar = () => {
    setSnackbarVisible(false);
    dispatch(resetError());
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.logoContainer}>
          <View style={styles.logoPlaceholder}>
            <Text style={styles.logoText}>PW</Text>
          </View>
          <Text style={styles.appName}>ParkWiz</Text>
          <Text style={styles.tagline}>Support & Operations Hub</Text>
        </View>

        <View style={styles.formContainer}>
          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            error={!!emailError}
            errorText={emailError}
            left={<TextInput.Icon icon="email" />}
            testID="email-input"
          />

          <TextInput
            label="Password"
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            error={!!passwordError}
            errorText={passwordError}
            left={<TextInput.Icon icon="lock" />}
            testID="password-input"
          />

          <Button
            mode="contained"
            onPress={handleLogin}
            style={styles.loginButton}
            disabled={isLoading}
            loading={isLoading}
            testID="login-button"
          >
            Login
          </Button>

          <TouchableOpacity onPress={handleForgotPassword} style={styles.forgotPasswordButton}>
            <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>Don't have an account?</Text>
          <Button
            mode="outlined"
            onPress={handleRegister}
            style={styles.registerButton}
            testID="register-button"
          >
            Register
          </Button>
        </View>

        <View style={styles.helpContainer}>
          <Text style={styles.helpText}>
            Contact admin for login credentials
          </Text>
        </View>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible || !!error}
        onDismiss={dismissSnackbar}
        action={{
          label: 'Dismiss',
          onPress: dismissSnackbar,
        }}
        duration={3000}
      >
        {error || 'An error occurred'}
      </Snackbar>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#0066CC',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  logoText: {
    fontSize: 40,
    fontWeight: 'bold',
    color: 'white',
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0066CC',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: '#757575',
  },
  formContainer: {
    marginBottom: 32,
  },
  loginButton: {
    marginTop: 16,
    paddingVertical: 8,
  },
  forgotPasswordButton: {
    alignSelf: 'center',
    marginTop: 16,
    padding: 8,
  },
  forgotPasswordText: {
    color: '#0066CC',
    fontSize: 14,
  },
  registerContainer: {
    alignItems: 'center',
  },
  registerText: {
    fontSize: 14,
    color: '#333333',
    marginBottom: 8,
  },
  registerButton: {
    width: '50%',
  },
  helpContainer: {
    marginTop: 24,
    alignItems: 'center',
  },
  helpText: {
    fontSize: 12,
    color: '#757575',
    textAlign: 'center',
  },
});

export default LoginScreen;
