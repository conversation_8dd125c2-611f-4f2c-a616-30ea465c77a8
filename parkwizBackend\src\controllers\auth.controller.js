const jwt = require('jsonwebtoken');
const { User, Role, Department } = require('../models');
const { AppError } = require('../middleware/error.middleware');

// Generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN
  });
};

// Register user
exports.register = async (req, res, next) => {
  try {
    const {
      employee_id,
      first_name,
      last_name,
      email,
      password,
      phone,
      role_id,
      department_id
    } = req.body;

    // Validate required fields
    if (!employee_id || !first_name || !last_name || !email || !password || !role_id || !department_id) {
      return next(new AppError('Please provide all required fields', 400));
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      where: { email }
    });

    if (existingUser) {
      return next(new AppError('User already exists with this email', 400));
    }

    // Verify role and department exist
    const role = await Role.findByPk(role_id);
    if (!role) {
      return next(new AppError('Selected role does not exist', 400));
    }

    const department = await Department.findByPk(department_id);
    if (!department) {
      return next(new AppError('Selected department does not exist', 400));
    }

    // Create user
    const user = await User.create({
      employee_id,
      first_name,
      last_name,
      email,
      password,
      phone,
      role_id,
      department_id,
      status: 'active' // Set default status to active
    });

    // Generate token
    const token = generateToken(user.id);

    // Remove password from response
    user.password = undefined;

    // Include role and department in response
    const userWithRelations = await User.findByPk(user.id, {
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      attributes: { exclude: ['password'] }
    });

    res.status(201).json({
      status: 'success',
      token,
      data: {
        user: userWithRelations
      }
    });
  } catch (error) {
    next(error);
  }
};

// Login user
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Check if email and password exist
    if (!email || !password) {
      return next(new AppError('Please provide email and password', 400));
    }

    // Find user
    const user = await User.findOne({
      where: { email },
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'permissions']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ]
    });

    // Check if user exists and password is correct
    if (!user || !(await user.checkPassword(password))) {
      return next(new AppError('Incorrect email or password', 401));
    }

    // Check if user is active
    if (user.status !== 'active') {
      return next(new AppError('Your account is not active', 401));
    }

    // Update last login
    await user.update({ last_login: new Date() });

    // Generate token
    const token = generateToken(user.id);

    // Remove password from response
    user.password = undefined;

    res.status(200).json({
      status: 'success',
      token,
      data: {
        user
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get current user
exports.getMe = async (req, res, next) => {
  try {
    // User is already available in req.user from auth middleware
    const user = await User.findByPk(req.user.id, {
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'permissions']
        },
        {
          model: Department,
          as: 'department',
          attributes: ['id', 'name']
        }
      ],
      attributes: { exclude: ['password'] }
    });

    res.status(200).json({
      status: 'success',
      data: {
        user
      }
    });
  } catch (error) {
    next(error);
  }
};

// Update password
exports.updatePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Find user
    const user = await User.findByPk(req.user.id);

    // Check if current password is correct
    if (!(await user.checkPassword(currentPassword))) {
      return next(new AppError('Current password is incorrect', 401));
    }

    // Update password
    user.password = newPassword;
    await user.save();

    // Generate new token
    const token = generateToken(user.id);

    // Remove password from response
    user.password = undefined;

    res.status(200).json({
      status: 'success',
      token,
      message: 'Password updated successfully'
    });
  } catch (error) {
    next(error);
  }
};
