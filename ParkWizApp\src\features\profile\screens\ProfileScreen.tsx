import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Text, Divider, Avatar, Button, Snackbar } from 'react-native-paper';
import { useAppSelector, useAppDispatch, logout } from '../../../store';
import * as ImagePicker from 'expo-image-picker';

// Components
import TextInput from '../../../components/common/TextInput';

const ProfileScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  
  // State for profile editing
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.first_name || '');
  const [lastName, setLastName] = useState(user?.last_name || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [profileImage, setProfileImage] = useState<string | null>(null);
  
  // UI state
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Handle profile image selection
  const handleSelectImage = async () => {
    try {
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (permissionResult.granted === false) {
        setSnackbarMessage('Permission to access camera roll is required!');
        setSnackbarVisible(true);
        return;
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });
      
      if (!result.canceled) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      setSnackbarMessage('Failed to select image');
      setSnackbarVisible(true);
    }
  };

  // Handle save profile
  const handleSaveProfile = () => {
    // In a real app, you would make an API call to update the user profile
    // For now, we'll just simulate a successful update
    setTimeout(() => {
      setIsEditing(false);
      setSnackbarMessage('Profile updated successfully');
      setSnackbarVisible(true);
    }, 1000);
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (user?.first_name && user?.last_name) {
      return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`;
    }
    return 'PW';
  };

  return (
    <View style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleSelectImage} disabled={!isEditing}>
            {profileImage ? (
              <Avatar.Image 
                size={100} 
                source={{ uri: profileImage }} 
                style={styles.avatar}
              />
            ) : (
              <Avatar.Text 
                size={100} 
                label={getUserInitials()} 
                style={styles.avatar}
                labelStyle={styles.avatarLabel}
              />
            )}
            {isEditing && (
              <View style={styles.editImageBadge}>
                <Text style={styles.editImageText}>Edit</Text>
              </View>
            )}
          </TouchableOpacity>
          
          <Text style={styles.name}>
            {user?.first_name} {user?.last_name}
          </Text>
          <Text style={styles.role}>
            {user?.role?.name || 'Employee'} • {user?.department?.name || 'Department'}
          </Text>
          
          <View style={styles.employeeIdContainer}>
            <Text style={styles.employeeIdLabel}>Employee ID</Text>
            <Text style={styles.employeeId}>{user?.employee_id || 'N/A'}</Text>
          </View>
        </View>

        <Divider style={styles.divider} />

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Personal Information</Text>
            {!isEditing ? (
              <Button 
                mode="text" 
                onPress={() => setIsEditing(true)}
                labelStyle={styles.editButtonLabel}
              >
                Edit
              </Button>
            ) : (
              <Button 
                mode="text" 
                onPress={() => setIsEditing(false)}
                labelStyle={styles.cancelButtonLabel}
              >
                Cancel
              </Button>
            )}
          </View>

          {isEditing ? (
            <View style={styles.editForm}>
              <TextInput
                label="First Name"
                value={firstName}
                onChangeText={setFirstName}
                style={styles.input}
              />
              
              <TextInput
                label="Last Name"
                value={lastName}
                onChangeText={setLastName}
                style={styles.input}
              />
              
              <TextInput
                label="Phone"
                value={phone}
                onChangeText={setPhone}
                keyboardType="phone-pad"
                style={styles.input}
              />
              
              <Button 
                mode="contained" 
                onPress={handleSaveProfile}
                style={styles.saveButton}
              >
                Save Changes
              </Button>
            </View>
          ) : (
            <View style={styles.infoContainer}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{user?.email || 'N/A'}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Phone</Text>
                <Text style={styles.infoValue}>{user?.phone || 'Not provided'}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Department</Text>
                <Text style={styles.infoValue}>{user?.department?.name || 'N/A'}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Role</Text>
                <Text style={styles.infoValue}>{user?.role?.name || 'N/A'}</Text>
              </View>
              
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Status</Text>
                <View style={[
                  styles.statusBadge, 
                  { backgroundColor: user?.status === 'active' ? '#e6f7ee' : '#fff1f0' }
                ]}>
                  <Text style={[
                    styles.statusText,
                    { color: user?.status === 'active' ? '#52c41a' : '#f5222d' }
                  ]}>
                    {user?.status === 'active' ? 'Active' : 'Inactive'}
                  </Text>
                </View>
              </View>
            </View>
          )}
        </View>

        <Divider style={styles.divider} />

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
          
          <Button 
            mode="outlined" 
            icon="lock-reset" 
            onPress={() => {
              // In a real app, navigate to change password screen
              setSnackbarMessage('Change password feature coming soon');
              setSnackbarVisible(true);
            }}
            style={styles.settingButton}
          >
            Change Password
          </Button>
          
          <Button 
            mode="outlined" 
            icon="bell-outline" 
            onPress={() => {
              // In a real app, navigate to notification settings
              setSnackbarMessage('Notification settings coming soon');
              setSnackbarVisible(true);
            }}
            style={styles.settingButton}
          >
            Notification Settings
          </Button>
          
          <Button 
            mode="outlined" 
            icon="theme-light-dark" 
            onPress={() => {
              // In a real app, toggle theme
              setSnackbarMessage('Theme settings coming soon');
              setSnackbarVisible(true);
            }}
            style={styles.settingButton}
          >
            Theme Settings
          </Button>
        </View>

        <Button 
          mode="contained" 
          icon="logout" 
          onPress={handleLogout}
          style={styles.logoutButton}
          buttonColor="#f5222d"
        >
          Logout
        </Button>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        action={{
          label: 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f7fa',
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginVertical: 24,
  },
  avatar: {
    backgroundColor: '#0066CC',
  },
  avatarLabel: {
    fontSize: 36,
  },
  editImageBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#0066CC',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  editImageText: {
    color: 'white',
    fontSize: 12,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333333',
  },
  role: {
    fontSize: 16,
    color: '#666666',
    marginTop: 4,
  },
  employeeIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    backgroundColor: '#f0f5ff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  employeeIdLabel: {
    fontSize: 14,
    color: '#0066CC',
    marginRight: 8,
  },
  employeeId: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0066CC',
  },
  divider: {
    marginVertical: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  editButtonLabel: {
    color: '#0066CC',
  },
  cancelButtonLabel: {
    color: '#f5222d',
  },
  infoContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666666',
  },
  infoValue: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  editForm: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  input: {
    marginBottom: 16,
  },
  saveButton: {
    marginTop: 8,
  },
  settingButton: {
    marginVertical: 8,
  },
  logoutButton: {
    marginVertical: 24,
  },
});

export default ProfileScreen;