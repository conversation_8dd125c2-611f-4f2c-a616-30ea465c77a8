import axios from 'axios';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Base URL for API requests
// Determine the appropriate API URL based on the environment
let apiBaseUrl = 'http://localhost:5000';  // Default for web

// Check if we're in a browser environment
if (typeof window !== 'undefined') {
  console.log('Running in browser environment');
  apiBaseUrl = 'http://localhost:5000';
} else {
  console.log('Running in non-browser environment');
  // For React Native / Expo, use the appropriate IP
  if (Platform.OS === 'android') {
    apiBaseUrl = 'http://********:5000';  // For Android emulator
  } else if (Platform.OS === 'ios') {
    apiBaseUrl = 'http://localhost:5000';  // For iOS simulator
  }
}

export const API_URL = apiBaseUrl;

// Create an axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add a request interceptor to add auth token to requests
api.interceptors.request.use(
  async (config) => {
    try {
      // Get token from localStorage for web environment
      // In a real app, you'd use platform detection to use SecureStore on native
      let token = null;
      
      // Use localStorage for web environment
      if (typeof localStorage !== 'undefined') {
        token = localStorage.getItem('auth_token');
      }
      
      // If token exists, add it to the headers
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    } catch (error) {
      console.error('Error setting auth token:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error);
      return Promise.reject(new Error('Network error. Please check your internet connection.'));
    }
    
    // Handle specific HTTP status codes
    switch (error.response.status) {
      case 401:
        // Unauthorized - could handle logout here
        console.error('Unauthorized:', error.response.data);
        break;
      case 403:
        // Forbidden
        console.error('Forbidden:', error.response.data);
        break;
      case 404:
        // Not found
        console.error('Resource not found:', error.response.data);
        break;
      case 500:
        // Server error
        console.error('Server error:', error.response.data);
        break;
      default:
        console.error('API error:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

export default api;