import { Middleware } from '@reduxjs/toolkit';
import { 
  login, loginSuccess, loginFailure,
  register, registerSuccess, registerFailure,
  checkAuth, loginSuccess as checkAuthSuccess, loginFailure as checkAuthFailure
} from '../store';
import api from '../services/api.service';

// API middleware
export const apiMiddleware: Middleware = ({ dispatch }) => (next) => async (action) => {
  next(action);

  // Handle login action
  if (login.match(action)) {
    try {
      const { email, password } = action.payload;
      
      console.log('Attempting login with:', { email, password: '******' });
      
      // Make API request to login endpoint
      const response = await api.post('/api/auth/login', { email, password });
      
      console.log('Login response:', response.data);
      
      // If successful, dispatch loginSuccess action
      if (response.data && response.data.token) {
        // Store token in localStorage for web environment
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('auth_token', response.data.token);
        }
        
        // Extract user data from the response
        const userData = response.data.data && response.data.data.user 
          ? response.data.data.user 
          : response.data.user;
        
        dispatch(loginSuccess({
          user: userData,
          token: response.data.token
        }));
      } else {
        // If response doesn't contain expected data
        dispatch(loginFailure('Invalid response from server'));
      }
    } catch (error) {
      // Handle error
      console.error('Login error:', error);
      
      // Extract error message
      let errorMessage = 'Authentication failed';
      
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
        errorMessage = 'No response from server. Please check if the backend is running.';
      } else if (error.message) {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
      }
      
      dispatch(loginFailure(errorMessage));
    }
  }
  
  // Handle register action
  if (register.match(action)) {
    try {
      const userData = action.payload;
      
      // Ensure role_id and department_id are valid UUIDs
      if (!userData.role_id || !userData.department_id) {
        console.error('Missing role_id or department_id');
        dispatch(registerFailure('Missing role or department information'));
        return;
      }
      
      // Log registration attempt (hide password)
      console.log('Attempting registration with:', { 
        ...userData, 
        password: '******' 
      });
      
      // Make API request to register endpoint
      // Make sure we're using the correct API endpoint path
      const response = await api.post('/api/auth/register', userData);
      
      // Log the full response for debugging
      console.log('Full registration response:', JSON.stringify(response.data));
      
      console.log('Registration response:', response.data);
      
      // If successful, dispatch registerSuccess action
      if (response.data) {
        let token = null;
        let userData = null;
        
        // Handle different response structures
        if (response.data.token) {
          token = response.data.token;
        } else if (response.data.data && response.data.data.token) {
          token = response.data.data.token;
        }
        
        // Extract user data from the response
        if (response.data.user) {
          userData = response.data.user;
        } else if (response.data.data && response.data.data.user) {
          userData = response.data.data.user;
        }
        
        if (token && userData) {
          // Store token in localStorage for web environment
          if (typeof localStorage !== 'undefined') {
            localStorage.setItem('auth_token', token);
          }
          
          dispatch(registerSuccess({
            user: userData,
            token: token
          }));
        } else {
          console.error('Missing token or user data in response:', response.data);
          dispatch(registerFailure('Invalid response from server: missing token or user data'));
        }
      } else {
        // If response doesn't contain expected data
        console.error('Empty response data');
        dispatch(registerFailure('Invalid response from server: empty response data'));
      }
    } catch (error) {
      // Handle error
      console.error('Registration error:', error);
      
      // Extract error message
      let errorMessage = 'Registration failed';
      
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
        errorMessage = 'No response from server. Please check if the backend is running.';
      } else if (error.message) {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
      }
      
      dispatch(registerFailure(errorMessage));
    }
  }
  
  // Handle checkAuth action
  if (checkAuth.match(action)) {
    try {
      console.log('Checking authentication status...');
      
      // Get token from localStorage
      let token = null;
      if (typeof localStorage !== 'undefined') {
        token = localStorage.getItem('auth_token');
      }
      
      if (!token) {
        console.log('No token found, user is not authenticated');
        dispatch(checkAuthFailure('No authentication token found'));
        return;
      }
      
      // Make API request to get current user
      const response = await api.get('/api/auth/me');
      
      console.log('Auth check response:', response.data);
      
      // If successful, dispatch loginSuccess action
      if (response.data && response.data.data && response.data.data.user) {
        dispatch(checkAuthSuccess({
          user: response.data.data.user,
          token: token
        }));
      } else {
        // If response doesn't contain expected data
        dispatch(checkAuthFailure('Invalid response from server'));
      }
    } catch (error) {
      // Handle error
      console.error('Auth check error:', error);
      
      // Clear token on auth failure
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('auth_token');
      }
      
      // Extract error message
      let errorMessage = 'Authentication check failed';
      
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        
        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
        errorMessage = 'No response from server. Please check if the backend is running.';
      } else if (error.message) {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
      }
      
      dispatch(checkAuthFailure(errorMessage));
    }
  }
};