import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// Screens
import DashboardScreen from '../features/dashboard/screens/DashboardScreen';
import TicketListScreen from '../features/tickets/screens/TicketListScreen';
import TicketDetailScreen from '../features/tickets/screens/TicketDetailScreen';
import CreateTicketScreen from '../features/tickets/screens/CreateTicketScreen';
import AttendanceHomeScreen from '../features/attendance/screens/AttendanceHomeScreen';
import LeaveRequestScreen from '../features/attendance/screens/LeaveRequestScreen';
import ProfileHomeScreen from '../features/auth/screens/ProfileHomeScreen';
import EditProfileScreen from '../features/auth/screens/EditProfileScreen';
import ChangePasswordScreen from '../features/auth/screens/ChangePasswordScreen';

// Create stack navigators for each tab
const TicketStack = createNativeStackNavigator();
const AttendanceStack = createNativeStackNavigator();
const ProfileStack = createNativeStackNavigator();

// Ticket Stack Navigator
const TicketsStackNavigator = () => {
  return (
    <TicketStack.Navigator>
      <TicketStack.Screen
        name="TicketList"
        component={TicketListScreen}
        options={{ headerShown: false }}
      />
      <TicketStack.Screen
        name="TicketDetail"
        component={TicketDetailScreen}
        options={{ title: 'Ticket Details' }}
      />
      <TicketStack.Screen
        name="CreateTicket"
        component={CreateTicketScreen}
        options={{ title: 'Create Ticket' }}
      />
    </TicketStack.Navigator>
  );
};

// Attendance Stack Navigator
const AttendanceStackNavigator = () => {
  return (
    <AttendanceStack.Navigator>
      <AttendanceStack.Screen
        name="AttendanceHome"
        component={AttendanceHomeScreen}
        options={{ headerShown: false }}
      />
      <AttendanceStack.Screen
        name="LeaveRequest"
        component={LeaveRequestScreen}
        options={{ title: 'Request Leave' }}
      />
    </AttendanceStack.Navigator>
  );
};

// Profile Stack Navigator
const ProfileStackNavigator = () => {
  return (
    <ProfileStack.Navigator>
      <ProfileStack.Screen
        name="ProfileHome"
        component={ProfileHomeScreen}
        options={{ headerShown: false }}
      />
      <ProfileStack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{ title: 'Edit Profile' }}
      />
      <ProfileStack.Screen
        name="ChangePassword"
        component={ChangePasswordScreen}
        options={{ title: 'Change Password' }}
      />
    </ProfileStack.Navigator>
  );
};

const Tab = createBottomTabNavigator();

const MainTabs = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#0066CC',
        tabBarInactiveTintColor: '#757575',
        tabBarStyle: {
          height: 70,
          paddingBottom: 8,
          paddingTop: 8,
          backgroundColor: '#FFFFFF',
          borderTopWidth: 1,
          borderTopColor: '#E0E0E0',
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontWeight: '500',
          marginTop: 2,
        },
        tabBarIconStyle: {
          marginTop: 2,
        },
        headerStyle: {
          backgroundColor: '#FFFFFF',
          elevation: 2,
          shadowOpacity: 0.1,
        },
        headerTitleStyle: {
          color: '#333333',
          fontWeight: 'bold',
        },
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarLabel: 'Dashboard',
          tabBarIcon: ({ color, size }) => (
            <Icon name="view-dashboard" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Tickets"
        component={TicketsStackNavigator}
        options={{
          tabBarLabel: 'Tickets',
          tabBarIcon: ({ color, size }) => (
            <Icon name="ticket" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Attendance"
        component={AttendanceStackNavigator}
        options={{
          tabBarLabel: 'Attendance',
          tabBarIcon: ({ color, size }) => (
            <Icon name="clock" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileStackNavigator}
        options={{
          tabBarLabel: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <Icon name="account" color={color} size={size} />
          ),
          headerShown: false,
        }}
      />
    </Tab.Navigator>
  );
};



export default MainTabs;
